// scripts/cleanDeliveryReceiptsData.js
'use strict';

const { Sequelize } = require('sequelize');
const getConnection = require('../src/infra/database/config/dbConnection');
const fs = require('fs');
const path = require('path');

async function cleanDeliveryReceiptsData(requisitionId) {
  if (!requisitionId) {
    console.error('Error: requisition_id parameter is required');
    process.exit(1);
  }

  try {
    const sequelize = await getConnection();
    console.log('Database connection established successfully.');

    // Begin transaction
    const transaction = await sequelize.transaction();

    try {
      // Get delivery receipt IDs for this requisition
      const drIds = await sequelize.query(
        `SELECT id FROM delivery_receipts WHERE requisition_id = :requisitionId`,
        {
          replacements: { requisitionId },
          type: Sequelize.QueryTypes.SELECT,
          transaction,
        }
      );

      if (drIds.length > 0) {
        const drIdList = drIds.map((dr) => dr.id);
        console.log(`Found ${drIdList.length} delivery receipts to process`);

        // Delete from related tables in correct order
        await sequelize.query(
          `DELETE FROM requisition_delivery_histories WHERE requisition_id = :requisitionId`,
          {
            replacements: { requisitionId },
            transaction,
          }
        );

        await sequelize.query(
          `DELETE FROM requisition_return_histories WHERE requisition_id = :requisitionId`,
          {
            replacements: { requisitionId },
            transaction,
          }
        );

        await sequelize.query(
          `DELETE FROM delivery_receipt_items_history 
            WHERE delivery_receipt_item_id IN (
              SELECT id FROM delivery_receipt_items WHERE dr_id IN (:drIdList)
            )`,
          {
            replacements: { drIdList },
            transaction,
          }
        );

        await sequelize.query(
          `DELETE FROM delivery_receipt_items WHERE dr_id IN (:drIdList)`,
          {
            replacements: { drIdList },
            transaction,
          }
        );

        // Delete attachments and files
        const attachmentRecords = await sequelize.query(
          `DELETE FROM attachments 
            WHERE model IN ('delivery_receipt_invoice', 'delivery_receipt')
            AND model_id IN (:drIdList)
            RETURNING path`,
          {
            replacements: { drIdList },
            type: Sequelize.QueryTypes.DELETE,
            transaction,
          }
        );

        // Delete physical files
        if (attachmentRecords[0].length > 0) {
          attachmentRecords[0].forEach((record) => {
            const filePath = path.join(record.path);
            if (fs.existsSync(filePath)) {
              fs.unlinkSync(filePath);
            }
          });
        }

        // Delete requisition item history records with delivered quantities
        await sequelize.query(
          `DELETE FROM requisition_item_histories 
            WHERE requisition_id = :requisitionId 
            AND quantity_delivered > 0`,
          {
            replacements: { requisitionId },
            transaction,
          }
        );
        
        // Finally delete delivery receipts
        await sequelize.query(
          `DELETE FROM delivery_receipts WHERE id IN (:drIdList)`,
          {
            replacements: { drIdList },
            transaction,
          }
        );
      }

      await transaction.commit();
      console.log(`Successfully cleaned delivery receipts data for requisition ${requisitionId}`);

    } catch (error) {
      await transaction.rollback();
      console.error('Error during cleanup process:', error);
      throw error;
    } finally {
      await sequelize.close();
      console.log('Database connection closed.');
    }
  } catch (error) {
    console.error('Unable to connect to the database:', error);
  }
}

// Check if running directly
if (require.main === module) {
  if (process.env.NODE_ENV === 'local') {
    const requisitionId = process.argv[2];
    if (!requisitionId) {
      console.error('Usage: node cleanDeliveryReceiptsData.js <requisition_id>');
      process.exit(1);
    }
    cleanDeliveryReceiptsData(requisitionId);
  } else {
    console.log('This script is only for local environment');
  }
}

module.exports = cleanDeliveryReceiptsData;
