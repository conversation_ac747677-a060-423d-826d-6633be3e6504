class CanvassService {
  constructor({
    db,
    utils,
    fastify,
    constants,
    clientErrors,
    noteService,
    canvassItemService,
    requisitionService,
    userRepository,
    roleRepository,
    attachmentRepository,
    canvassItemRepository,
    requisitionRepository,
    canvassApproverRepository,
    canvassRequisitionRepository,
    canvassItemSupplierRepository,
    requisitionItemListRepository,
    purchaseOrderCancelledItemsRepository,
    approverService,
    supplierRepository,
  }) {
    this.db = db;
    this.utils = utils;
    this.fastify = fastify;
    this.constants = constants;
    this.clientErrors = clientErrors;
    this.noteService = noteService;
    this.roleRepository = roleRepository;
    this.userRepository = userRepository;
    this.attachmentRepository = attachmentRepository;
    this.canvassItemRepository = canvassItemRepository;
    this.requisitionRepository = requisitionRepository;
    this.canvassApproverRepository = canvassApproverRepository;
    this.canvassRequisitionRepository = canvassRequisitionRepository;
    this.canvassItemSupplierRepository = canvassItemSupplierRepository;
    this.requisitionItemListRepository = requisitionItemListRepository;
    this.purchaseOrderCancelledItemsRepository =
      purchaseOrderCancelledItemsRepository;
    this.canvassItemService = canvassItemService;
    this.requisitionService = requisitionService;
    this.approverService = approverService;
    this.supplierRepository = supplierRepository;
  }

  async getExistingCanvass(canvassId, options = {}) {
    const existingCanvass = await this.canvassRequisitionRepository.getCanvass(
      canvassId,
      options,
    );

    if (!existingCanvass) {
      throw this.clientErrors.NOT_FOUND({
        message: `Canvass not found with ID: ${canvassId}`,
      });
    }

    return existingCanvass;
  }

  async getAllCanvass(payload) {
    const canvassList =
      await this.canvassRequisitionRepository.getAllCanvass(payload);
    return canvassList;
  }

  async canvassCreateValidation(payload = {}) {
    const { canvassId, requisitionId, creatorId, isDraft = true } = payload;
    const { CANVASS_STATUS } = this.constants.canvass;
    const { REQUISITION_STATUS } = this.constants.requisition;

    const existingRequisition = await this.requisitionRepository.findOne({
      where: { id: requisitionId },
    });

    if (!existingRequisition) {
      throw this.clientErrors.NOT_FOUND({
        message:
          'Requisition not found. Cannot create canvass without a valid requisition',
      });
    }

    // Check if requisition is force closed
    if (
      existingRequisition.status === REQUISITION_STATUS.CLOSED ||
      existingRequisition.forceClosedAt
    ) {
      throw this.clientErrors.BAD_REQUEST({
        message:
          'Cannot create or modify canvass. Requisition has been force closed.',
        details: {
          requisitionId,
          status: existingRequisition.status,
          forceClosedAt: existingRequisition.forceClosedAt,
        },
      });
    }

    if (!existingRequisition.assignedTo) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Cannot create canvass. No assigned purchasing staff.',
      });
    }

    if (existingRequisition.assignedTo !== creatorId) {
      throw this.clientErrors.FORBIDDEN({
        message:
          'Cannot create canvass. Requisition is assigned to another user',
      });
    }

    const existingCanvass =
      await this.canvassRequisitionRepository.getExistingCanvass({
        canvassId,
        requisitionId,
      });

    // if (existingCanvass?.status === CANVASS_STATUS.APPROVED) {
    //   throw this.clientErrors.BAD_REQUEST({
    //     message:
    //       'Cannot modify canvass - this requisition already has an approved canvass. No further changes are allowed.',
    //   });
    // }

    // if (existingCanvass?.status === CANVASS_STATUS.FOR_APPROVAL) {
    //   throw this.clientErrors.BAD_REQUEST({
    //     message:
    //       'Canvass sheet is currently for approval, changes is not allowed',
    //   });
    // }

    const isAllowDraft =
      isDraft &&
      existingCanvass &&
      existingCanvass?.status !== CANVASS_STATUS.DRAFT;

    if (isAllowDraft) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Cannot save as draft - this requisition already has a canvass in ${existingCanvass.status} status.`,
      });
    }

    return existingRequisition;
  }

  async createCanvass(payload = {}) {
    const {
      isDraft,
      assignedTo,
      transaction,
      requisitionId,
      addItems = [],
      updateItems = [],
      deleteItems = [],
      id: canvassId,
      userFromToken,
      canvassType,
    } = payload;

    const { REQUISITION_STATUS } = this.constants.requisition;
    const { CANVASS_STATUS, CANVASS_ITEM_STATUS } = this.constants.canvass;
    const numberField = isDraft ? 'draftCsNumber' : 'csNumber';

    let canvassData =
      await this.canvassRequisitionRepository.getExistingCanvass({
        canvassId,
        requisitionId,
      });

    const { csLetter, csNumber } = await this.#generateCSNumberCode(
      isDraft,
      canvassData?.id,
      transaction,
    );

    if (!isDraft && canvassData?.id) {
      const itemCheck = await this.checkItemsValidity({
        transaction,
        requisitionId,
        canvassType,
        canvassId,
        isSubmitting: false,
      });

      const { data: existingCanvassItems } =
        await this.canvassItemRepository.findAll({
          where: {
            requisitionId,
            canvassRequisitionId: canvassData.id,
            status: CANVASS_ITEM_STATUS.NEW,
          },
          paginate: false,
          transaction,
        });

      const filteredItemCheck = itemCheck.filter((item) =>
        existingCanvassItems.some(
          (existingItem) => existingItem.requisitionItemListId === item.id,
        ),
      );

      const overCanvassedItems = filteredItemCheck
        .filter((item) => {
          return (
            item.canvassedQty >= item.requestedQty &&
            !deleteItems.some((deleteItem) => deleteItem.id === item.id)
          );
        })
        .map((item) => ({
          id: item.id,
          itemName: item.itemName,
          itemType: item.itemType,
        }));

      const hasOverCanvassed = !!overCanvassedItems.length;
      if (hasOverCanvassed)
        return {
          hasOverCanvassed,
          overCanvassedItems,
        };
    }

    let isPartial = !isDraft;
    if (!isDraft) {
      isPartial = await this.#checkPartialCanvass({
        addItems,
        updateItems,
        deleteItems,
        requisitionId,
        canvassId: canvassData?.id,
      });
    }

    if (isPartial) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Every canvassed item must have at least one supplier.',
      });
    }

    const nonDraftStatus = isPartial
      ? CANVASS_STATUS.PARTIAL
      : CANVASS_STATUS.FOR_APPROVAL;

    const csStatus = isDraft ? CANVASS_STATUS.DRAFT : nonDraftStatus;
    const isCanvassNew = !canvassData;

    if (isCanvassNew) {
      canvassData = await this.canvassRequisitionRepository.create(
        {
          csLetter,
          requisitionId,
          [numberField]: csNumber,
          status: csStatus,
        },
        { transaction },
      );
    } else {
      await this.canvassRequisitionRepository.update(
        { id: canvassData.id },
        {
          status: csStatus,
          ...(!isDraft && { csLetter }),
          ...(!isDraft && { csNumber }),
          updatedAt: new Date().toISOString(),
        },
        { transaction },
      );
    }

    await this.#processCanvassItems({
      isCanvassNew,
      canvassId: canvassData.id,
      requisitionId,
      addItems,
      updateItems,
      deleteItems,
      isDraft,
      assignedTo,
      transaction,
      userFromToken,
    });

    await this.checkItemsValidity({
      canvassType,
      transaction,
      requisitionId,
    });

    const hasCanvassItems = await this.canvassItemRepository.findOne({
      where: {
        canvassRequisitionId: canvassData.id,
      },
      transaction,
    });

    if (!hasCanvassItems && !isDraft) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Canvass must have at least one item.`,
      });
    }

    const canvassItemStatus = isDraft
      ? CANVASS_ITEM_STATUS.NEW
      : CANVASS_ITEM_STATUS.FOR_APPROVAL;

    await this.canvassItemRepository.update(
      {
        requisitionId,
        canvassRequisitionId: canvassData.id,
      },
      {
        status: canvassItemStatus,
      },
      { transaction },
    );

    if (csStatus === CANVASS_STATUS.FOR_APPROVAL) {
      await this.#generateCanvassApprovers({
        assignedTo,
        transaction,
        canvassRequisitionId: canvassData.id,
      });
    }

    const requisition = await this.requisitionRepository.findOne({
      attributes: ['status'],
      where: { id: requisitionId },
      transaction,
    });

    if (requisition.status !== REQUISITION_STATUS.RS_IN_PROGRESS) {
      await this.requisitionRepository.update(
        { id: requisitionId },
        { status: REQUISITION_STATUS.RS_IN_PROGRESS },
        { transaction },
      );
    }

    const isSupplierUpdated = updateItems.some((item) =>
      item.suppliers?.some((supplier) => supplier.id),
    );

    return { ...canvassData, isSupplierUpdated };
  }

  async #generateCSNumberCode(isDraft = false, canvassId, transaction = null) {
    let whereClause;
    const { CANVASS_STATUS } = this.constants.canvass;
    const numberField = isDraft ? 'draftCsNumber' : 'csNumber';

    if (isDraft) {
      whereClause = {
        status: CANVASS_STATUS.DRAFT,
        draftCsNumber: {
          [this.db.Sequelize.Op.ne]: null,
        },
        ...(canvassId && { id: { [this.db.Sequelize.Op.ne]: canvassId } }),
      };
    } else {
      whereClause = {
        status: {
          [this.db.Sequelize.Op.ne]: CANVASS_STATUS.DRAFT,
        },
        csNumber: {
          [this.db.Sequelize.Op.ne]: null,
        },
        ...(canvassId && { id: { [this.db.Sequelize.Op.ne]: canvassId } }),
      };
    }

    const lastCanvass = await this.canvassRequisitionRepository.findOne({
      where: whereClause,
      order: [[numberField, 'DESC']],
      transaction,
      lock: true,
    });

    const { nextLetter, nextNumber } = this.utils.getNextNumberAndLetter(
      lastCanvass?.[numberField],
      lastCanvass?.csLetter,
    );

    return {
      csNumber: nextNumber,
      csLetter: nextLetter,
    };
  }

  async #generateCanvassApprovers(payload) {
    const { USER_TYPES } = this.constants.user;
    const { canvassRequisitionId, transaction, assignedTo } = payload;
    const { CANVASS_APPROVER_STATUS } = this.constants.canvass;
    const approvers = await this.canvassApproverRepository.findAll({
      paginate: false,
      where: { canvassRequisitionId },
    });

    if (approvers.total > 0) {
      const rejectedApprovers = approvers.data.filter(
        (approver) => approver.status === CANVASS_APPROVER_STATUS.REJECTED,
      );

      if (
        rejectedApprovers.length > 0 &&
        Math.min(rejectedApprovers.map((approver) => approver.level)) >= 2
      ) {
        // 2 - purchase head level
        await this.canvassApproverRepository.update(
          {
            canvassRequisitionId,
            level: {
              [this.db.Sequelize.Op.ne]: 1,
            },
          },
          {
            status: CANVASS_APPROVER_STATUS.PENDING,
            overrideBy: null,
          },
          { transaction },
        );
      } else if (rejectedApprovers.length > 0) {
        await this.canvassApproverRepository.update(
          {
            canvassRequisitionId,
            status: CANVASS_APPROVER_STATUS.REJECTED,
          },
          {
            status: CANVASS_APPROVER_STATUS.PENDING,
            overrideBy: null,
          },
          { transaction },
        );
      }

      return;
    }

    /* Role Fetching */
    const [supervisorRole, purchasingHeadRole, managementRole] =
      await Promise.all([
        this.roleRepository.findOne({ where: { name: USER_TYPES.SUPERVISOR } }),
        this.roleRepository.findOne({
          where: { name: USER_TYPES.PURCHASING_HEAD },
        }),
        this.roleRepository.findOne({ where: { name: USER_TYPES.MANAGEMENT } }),
      ]);

    const requiredApproverRoles =
      supervisorRole && purchasingHeadRole && managementRole;

    if (!requiredApproverRoles) {
      throw this.clientErrors.BAD_REQUEST({
        message:
          'Required approver roles for the canvass management are not set',
      });
    }

    /* Approvers Fetching */
    const [assignedUser, userPurchaseHead, managementUsers] = await Promise.all(
      [
        this.userRepository.getUserById(assignedTo, { paranoid: false }),
        this.userRepository.findOne({
          paranoid: false,
          where: { roleId: purchasingHeadRole.id },
        }),
        this.userRepository.findAll({
          paranoid: false,
          paginate: false,
          where: { roleId: managementRole.id },
        }),
      ],
    );

    const initialCanvassApprovers = [
      {
        level: 1,
        roleId: supervisorRole.id,
        ...(assignedUser?.supervisor?.id && {
          userId: assignedUser?.supervisor?.id,
        }),
      },
      {
        level: 2,
        roleId: purchasingHeadRole.id,
        ...(userPurchaseHead && { userId: userPurchaseHead.id }),
      },
    ];

    if (managementUsers.total === 0) {
      initialCanvassApprovers.push(
        ...[3, 4].map((level) => ({
          level,
          roleId: managementRole.id,
        })),
      );
    } else {
      initialCanvassApprovers.push(
        ...managementUsers.data.map((user, index) => ({
          level: index + 3,
          roleId: managementRole.id,
          userId: user.id,
        })),
      );

      if (managementUsers.total === 1) {
        initialCanvassApprovers.push({
          level: 4,
          roleId: managementRole.id,
        });
      }
    }

    await this.canvassApproverRepository.bulkCreate(
      initialCanvassApprovers.map((approver) => ({
        ...approver,
        canvassRequisitionId,
      })),
      { transaction },
    );
  }

  async getCanvassApprovers(canvassId) {
    const canvassApprovers =
      await this.canvassApproverRepository.getAllCanvassApprovers(canvassId);

    return canvassApprovers.data;
  }

  async approveCanvass(payload = {}) {
    const { USER_TYPES } = this.constants.user;
    const { existingCanvass, approver, canvassSuppliers, transaction } =
      payload;
    const { CANVASS_STATUS, CANVASS_APPROVER_STATUS, CANVASS_ITEM_STATUS } =
      this.constants.canvass;
    const { REQUISITION_STATUS } = this.constants.requisition;
    const isTOM = existingCanvass?.requisition?.type.includes('tom'); // move this to a config variable
    const forApprovalStatuses = [
      CANVASS_STATUS.FOR_APPROVAL,
      CANVASS_STATUS.REJECTED,
    ];
    const isReadyForApproval = !forApprovalStatuses.includes(
      existingCanvass.status,
    );

    // Check if canvass is cancelled due to force close
    if (existingCanvass.status === CANVASS_STATUS.CS_CANCELLED) {
      throw this.clientErrors.BAD_REQUEST({
        message:
          'Cannot approve canvass. Canvass has been cancelled due to force close.',
        details: {
          canvassId: existingCanvass.id,
          status: existingCanvass.status,
        },
      });
    }

    // Check if parent requisition is force closed
    if (
      existingCanvass.requisition?.status === REQUISITION_STATUS.CLOSED ||
      existingCanvass.requisition?.forceClosedAt
    ) {
      throw this.clientErrors.BAD_REQUEST({
        message:
          'Cannot approve canvass. Parent requisition has been force closed.',
        details: {
          canvassId: existingCanvass.id,
          requisitionId: existingCanvass.requisitionId,
          requisitionStatus: existingCanvass.requisition.status,
        },
      });
    }

    if (isReadyForApproval) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Canvass sheet is not for approval`,
      });
    }

    const approvers =
      await this.canvassApproverRepository.getAllCanvassApprovers(
        existingCanvass?.id,
      );

    const currentApproverIndex = approvers.data.findIndex((approverRecord) => {
      const canvassAltApproverId = approverRecord.approver.userLeaves.length
        ? approverRecord.approver.userLeaves[0].canvassAltUser.id
        : 0;

      const isApprover =
        approverRecord.userId === approver.id ||
        approverRecord.altApproverId === approver.id ||
        canvassAltApproverId === approver.id;

      return isApprover;
    });

    if (currentApproverIndex === -1) {
      throw this.clientErrors.FORBIDDEN({
        message: `You are not authorized to approve this canvass`,
      });
    }

    const currentApprover = approvers.data[currentApproverIndex];

    if (currentApprover.status === CANVASS_STATUS.APPROVED) {
      throw this.clientErrors.BAD_REQUEST({
        message: `You already approved this canvass`,
      });
    }

    if (!(USER_TYPES.PURCHASING_HEAD === approver.role.name)) {
      if (currentApprover?.isAdhoc) {
        const primaryApprover = approvers.data.find(
          (approver) =>
            approver.level === currentApprover?.level && !approver?.isAdhoc,
        );
      }

      if (currentApprover.level <= 2) {
        // Purchase Head Below Approvers

        let previousApprover = null;

        if (currentApproverIndex !== 0) {
          previousApprover = approvers.data[currentApproverIndex - 1];
        }

        if (previousApprover?.status === CANVASS_APPROVER_STATUS.PENDING) {
          await this.approverService.overrideApprover({
            model: 'canvass',
            modelId: existingCanvass.id,
            approverId: approver.id,
            status: 'approved',
            transaction,
            requisitionId: existingCanvass.requisitionId,
          });
        }
      } else {
        const level2Approvers = approvers.data.filter(
          (approver) => approver.level === 2,
        );

        for (const level2Approver of level2Approvers) {
          if (level2Approver.status !== CANVASS_APPROVER_STATUS.APPROVED) {
            throw this.clientErrors.BAD_REQUEST({
              message: `Level 2 ${
                level2Approver.isAdhoc ? 'adhoc ' : ''
              }approver must approve first`,
            });
          }
        }
      }
    } else {
      if (
        existingCanvass.requisition.type === 'ofm' &&
        currentApprover.isAdhoc === false &&
        currentApprover.level === 2 &&
        currentApprover.status === 'pending'
      ) {
        if (canvassSuppliers.length === 0) {
          throw this.clientErrors.BAD_REQUEST({
            message: `Canvassed item must have at least one supplier.`,
          });
        }

        await this.validateSelectedSuppliers({
          transaction,
          requisitionId: existingCanvass.requisition.id,
          selectedSuppliers: canvassSuppliers,
        });
      }

      await this.approverService.overrideApprover({
        model: 'canvass',
        modelId: existingCanvass.id,
        approverId: approver.id,
        status: 'approved',
        transaction,
        requisitionId: existingCanvass.requisitionId,
      });

      const adhocApprovers = approvers.data.filter(
        (approver) =>
          approver.isAdhoc &&
          approver.status === CANVASS_APPROVER_STATUS.PENDING &&
          approver.level === 1,
      );

      if (adhocApprovers.length > 0) {
        await this.approverService.overrideApprover({
          model: 'canvass',
          modelId: existingCanvass.id,
          approverId: approver.id,
          status: 'approved',
          transaction,
          requisitionId: existingCanvass.requisitionId,
        });
      }

      await this.#updateCanvassSuppliers({
        transaction,
        canvassSuppliers,
        canvassId: existingCanvass.id,
        isTOM,
      });
    }

    await this.canvassApproverRepository.update(
      {
        userId: currentApprover.userId,
        canvassRequisitionId: existingCanvass.id,
      },
      { status: CANVASS_APPROVER_STATUS.APPROVED },
      { transaction },
    );

    const updatedApprovers = approvers.data.map((approver) => ({
      ...approver,
      status:
        approver.userId === currentApprover.userId
          ? CANVASS_APPROVER_STATUS.APPROVED
          : approver.status,
    }));

    const allApproved = updatedApprovers.every(
      (approver) => approver.status === CANVASS_APPROVER_STATUS.APPROVED,
    );

    if (allApproved) {
      await this.canvassRequisitionRepository.update(
        { id: existingCanvass.id },
        { status: CANVASS_STATUS.APPROVED },
        { transaction },
      );

      await this.canvassItemRepository.update(
        { canvassRequisitionId: existingCanvass.id },
        { status: CANVASS_ITEM_STATUS.APPROVED },
        { transaction },
      );
    }

    return allApproved;
  }

  async addAdhocApprover(payload = {}) {
    const { canvassId, approver, creatorId, transaction } = payload;
    const { CANVASS_APPROVER_STATUS } = this.constants.canvass;

    const approvers = await this.canvassApproverRepository.findAll({
      paginate: false,
      where: {
        userId: creatorId,
        canvassRequisitionId: canvassId,
      },
      order: [
        ['level', 'ASC'],
        ['isAdhoc', 'ASC'],
      ],
    });

    const altApprovers = await this.canvassApproverRepository.findAll({
      paginate: false,
      where: {
        altApproverId: creatorId,
        canvassRequisitionId: canvassId,
      },
      order: [
        ['level', 'ASC'],
        ['isAdhoc', 'ASC'],
      ],
    });

    if (!approvers.data.length && !altApprovers.data.length) {
      throw this.clientErrors.FORBIDDEN({
        message: 'Only existing approvers can add adhoc approvers',
      });
    }

    const creatorApprover = approvers.data[0] || altApprovers.data[0];

    if (creatorApprover.isAdhoc) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Adhoc approvers cannot add other adhoc approvers',
      });
    }

    const isExistingApprover = await this.canvassApproverRepository.findOne({
      where: {
        canvassRequisitionId: canvassId,
        userId: approver.id,
      },
    });

    if (isExistingApprover) {
      return;
    }

    const existingAdhoc = await this.canvassApproverRepository.findOne({
      where: {
        canvassRequisitionId: canvassId,
        level: creatorApprover.level,
        isAdhoc: true,
      },
    });

    if (existingAdhoc) {
      await this.canvassApproverRepository.update(
        { id: existingAdhoc.id },
        { userId: approver.id },
        { transaction },
      );

      return;
    }

    await this.canvassApproverRepository.create(
      {
        canvassRequisitionId: canvassId,
        userId: approver.id,
        level: creatorApprover.level,
        isAdhoc: true,
        roleId: approver.role.id,
      },
      { transaction },
    );
  }

  async rejectCanvass(payload = {}) {
    const { existingCanvass, userFromToken, transaction } = payload;
    const { CANVASS_STATUS, CANVASS_APPROVER_STATUS } = this.constants.canvass;
    const { USER_TYPES } = this.constants.user;
    const approverId = userFromToken.id;

    const approvers =
      await this.canvassApproverRepository.getAllCanvassApprovers(
        existingCanvass?.id,
      );

    const currentApprover = approvers.data.find(
      (approver) =>
        approver.userId === approverId || approver.altApproverId === approverId,
    );

    const currentApproverIndex = approvers.data.findIndex((approverRecord) => {
      const canvassAltApproverId = approverRecord?.approver?.userLeaves?.length
        ? approverRecord.approver?.userLeaves[0].canvassAltUser.id
        : 0;

      const isApprover =
        approverRecord.userId === approverId ||
        approverRecord.altApproverId === approverId ||
        canvassAltApproverId === approverId;

      return isApprover;
    });

    if (!currentApprover) {
      throw this.clientErrors.FORBIDDEN({
        message: 'You are not authorized to reject this canvass',
      });
    }

    if (currentApprover.status === CANVASS_APPROVER_STATUS.REJECTED) {
      return;
    }

    if (currentApprover.status === CANVASS_APPROVER_STATUS.APPROVED) {
      throw this.clientErrors.BAD_REQUEST({
        message: 'Cannot reject after approval',
      });
    }

    if (currentApprover.isAdhoc) {
      const primaryApprover = approvers.data.find(
        (approver) =>
          approver.level === currentApprover.level && !approver.isAdhoc,
      );

      if (
        primaryApprover?.status !== CANVASS_APPROVER_STATUS.APPROVED &&
        currentApprover.level > 2
      ) {
        throw this.clientErrors.BAD_REQUEST({
          message: `Level ${currentApprover.level} primary approver must approve first`,
        });
      }
    }

    if (
      !(
        USER_TYPES.PURCHASING_HEAD === userFromToken.role.name &&
        currentApprover.level > 2
      )
    ) {
      if (currentApprover.level <= 2) {
        const previousLevelApprovers = approvers.data.filter(
          (approver) => approver.level < currentApprover.level,
        );

        let previousApprover = null;

        if (currentApproverIndex !== 0) {
          previousApprover = approvers.data[currentApproverIndex - 1];
        }

        if (previousApprover?.status === CANVASS_APPROVER_STATUS.PENDING) {
          await this.approverService.overrideApprover({
            model: 'canvass',
            modelId: existingCanvass.id,
            approverId: userFromToken.id,
            transaction,
            requisitionId: existingCanvass.requisitionId,
            status: 'rejected',
          });
        }
        /*
        for (const prevApprover of previousLevelApprovers) {
          if (prevApprover.status !== CANVASS_APPROVER_STATUS.APPROVED) {
            throw this.clientErrors.BAD_REQUEST({
              message: `Level ${prevApprover.level} ${
                prevApprover.isAdhoc ? 'adhoc ' : ''
              }approver must approve first`,
            });
          }
        }

        */
      } else {
        const level2Approvers = approvers.data.filter(
          (approver) => approver.level === 2,
        );

        for (const level2Approver of level2Approvers) {
          if (level2Approver.status !== CANVASS_APPROVER_STATUS.APPROVED) {
            throw this.clientErrors.BAD_REQUEST({
              message: `Level 2 ${
                level2Approver.isAdhoc ? 'adhoc ' : ''
              }approver must approve first`,
            });
          }
        }
      }
    } else {
      await this.approverService.overrideApprover({
        model: 'canvass',
        modelId: existingCanvass.id,
        approverId: userFromToken.id,
        transaction,
        requisitionId: existingCanvass.requisitionId,
        status: 'rejected',
      });
    }

    await this.canvassApproverRepository.update(
      {
        userId: currentApprover.userId,
        canvassRequisitionId: existingCanvass.id,
      },
      { status: CANVASS_APPROVER_STATUS.REJECTED },
      { transaction },
    );

    await this.canvassRequisitionRepository.update(
      { id: existingCanvass.id },
      { status: CANVASS_STATUS.REJECTED },
      { transaction },
    );
  }

  async #checkPartialCanvass({
    canvassId,
    addItems = [],
    updateItems = [],
    deleteItems = [],
  }) {
    const existingCanvassItems = canvassId
      ? await this.canvassItemRepository.findAll({
          where: {
            canvassRequisitionId: canvassId,
            id: {
              [this.db.Sequelize.Op.notIn]: deleteItems.map((item) => item.id),
            },
          },
          include: [
            {
              association: 'suppliers',
              as: 'suppliers',
              required: false,
            },
          ],
          paginate: false,
        })
      : { data: [], total: 0 };

    const mergedCanvassItems = [
      ...existingCanvassItems.data,
      ...updateItems,
      ...addItems,
    ];

    const canvassItemsMap = new Map(
      mergedCanvassItems.map((item) => [
        item.requisitionItemListId,
        {
          ...item,
          hasSuppliers: Boolean(item.suppliers?.length),
        },
      ]),
    );

    const allItemsHaveSuppliers = Array.from(canvassItemsMap.values()).every(
      (item) => item.hasSuppliers,
    );

    return !allItemsHaveSuppliers;
  }

  async #updateCanvassSuppliers({
    canvassSuppliers,
    transaction,
    canvassId,
    isTOM,
  }) {
    const canvassItemList = await this.canvassItemRepository.findAll({
      paginate: false,
      where: { canvassRequisitionId: canvassId },
      include: [
        {
          association: 'suppliers',
          as: 'suppliers',
          attributes: ['id', 'isSelected'],
        },
      ],
    });

    const allExistingSupplierIds = new Set(
      canvassItemList.data.flatMap((item) =>
        item.suppliers.map((supplier) => supplier.id),
      ),
    );

    const invalidSupplierIds = canvassSuppliers
      .filter((selection) => !allExistingSupplierIds.has(selection.id))
      .map((selection) => selection.id);

    if (invalidSupplierIds.length > 0) {
      throw this.clientErrors.BAD_REQUEST({
        message: `Invalid selection: Supplier ID(s) ${invalidSupplierIds.join(', ')} are not associated with any items in this canvass sheet.`,
      });
    }

    const itemsWithoutSelection = canvassItemList.data.filter((canvassItem) => {
      const itemSupplierIds = canvassItem.suppliers.map(
        (supplier) => supplier.id,
      );
      const hasSelectedSupplier = itemSupplierIds.some((supplierId) => {
        const supplierSelection = canvassSuppliers.find(
          (selection) =>
            selection.id === supplierId && selection.isSelected === true,
        );

        return supplierSelection !== undefined;
      });

      return !hasSelectedSupplier;
    });

    if (itemsWithoutSelection.length > 0 && !isTOM) {
      throw this.clientErrors.BAD_REQUEST({
        message: `All canvass items must have at least one selected supplier. Missing selections for ${itemsWithoutSelection.length} canvass item(s).`,
      });
    }

    const updateSupplierItems = canvassItemList.data.flatMap((canvassItem) => {
      return canvassItem.suppliers.map((supplier) => {
        const supplierSelection = canvassSuppliers.find(
          (selection) => selection.id === supplier.id,
        );

        return this.canvassItemSupplierRepository.update(
          { id: supplier.id },
          { isSelected: isTOM ? true : supplierSelection?.isSelected || false },
          { transaction },
        );
      });
    });

    try {
      await Promise.all(updateSupplierItems);
    } catch (error) {
      this.fastify.log.error('[Error] Update Supplier Items Selection');
      this.fastify.log.error(error);
      throw error;
    }
  }

  async removeAdhocApprover(payload = {}) {
    const { canvassId, primaryApproverId } = payload;
    const { CANVASS_APPROVER_STATUS } = this.constants.canvass;

    const approvers = await this.canvassApproverRepository.findAll({
      paginate: false,
      where: {
        canvassRequisitionId: canvassId,
      },
    });

    const primaryApprover = approvers.data.find(
      (approver) =>
        (approver.userId === primaryApproverId ||
          approver.altApproverId === primaryApproverId) &&
        !approver.isAdhoc,
    );

    if (!primaryApprover) {
      throw this.clientErrors.FORBIDDEN({
        message: 'You are not authorized to remove adhoc approvers',
      });
    }

    const adhocApprover = approvers.data.find(
      (approver) =>
        approver.isAdhoc && approver.level === primaryApprover.level,
    );

    if (!adhocApprover) {
      throw this.clientErrors.NOT_FOUND({
        message: `No adhoc approver found for level ${primaryApprover.level}`,
      });
    }

    await this.canvassApproverRepository.destroy({
      id: adhocApprover.id,
    });
  }

  async cascadeRoleApprover({ userId, roleId, transaction }) {
    const { USER_TYPES } = this.constants.user;
    const { CANVASS_APPROVER_STATUS } = this.constants.canvass;
    const options = transaction ? { transaction } : {};

    const role = await this.roleRepository.findOne({ where: { id: roleId } });
    const isPurchasingHead = role?.name === USER_TYPES.PURCHASING_HEAD;
    const isCascadeAllow = [
      USER_TYPES.PURCHASING_HEAD,
      USER_TYPES.MANAGEMENT,
    ].includes(role?.name);

    if (!isCascadeAllow) {
      return;
    }

    const queryWhere = {
      roleId,
      isAdhoc: false,
      status: CANVASS_APPROVER_STATUS.PENDING,
      level: isPurchasingHead ? 2 : { [this.db.Sequelize.Op.in]: [3, 4] },
      [this.db.Sequelize.Op.or]: [{ userId: null }, { userId }],
    };

    const pendingApprovers = await this.canvassApproverRepository.findAll({
      paginate: false,
      where: queryWhere,
    });

    if (pendingApprovers.total === 0) {
      this.fastify.log.info(
        `[INFO] No pending ${role.name} approvers found to cascade`,
      );
      return;
    }

    try {
      let updatePromises = [];

      if (isPurchasingHead) {
        updatePromises = pendingApprovers.data
          .filter((approver) => approver.userId === null)
          .map((approver) => {
            this.fastify.log.info(
              `[INFO] Cascading approver ${approver.id} to user ${userId}`,
            );
            return this.canvassApproverRepository.update(
              { id: approver.id },
              { userId },
              options,
            );
          });
      } else {
        const approversByCanvass = pendingApprovers.data.reduce(
          (acc, approver) => {
            (acc[approver.canvassRequisitionId] =
              acc[approver.canvassRequisitionId] || []).push(approver);
            return acc;
          },
          {},
        );

        updatePromises = Object.values(approversByCanvass)
          .map((approvers) => {
            const level3Approver = approvers.find((a) => a.level === 3);
            const level4Approver = approvers.find((a) => a.level === 4);
            const existingLevel3 = approvers.some(
              (a) => a.level === 3 && String(a.userId) === String(userId),
            );
            const existingLevel4 = approvers.some(
              (a) => a.level === 4 && String(a.userId) === String(userId),
            );

            if (existingLevel3 || existingLevel4) {
              return null;
            }

            if (level3Approver?.userId === null) {
              return this.canvassApproverRepository.update(
                { id: level3Approver.id },
                { userId },
                options,
              );
            }

            if (level4Approver?.userId === null) {
              return this.canvassApproverRepository.update(
                { id: level4Approver.id },
                { userId },
                options,
              );
            }

            return null;
          })
          .filter(Boolean);
      }

      await Promise.all(updatePromises);
      this.fastify.log.info(
        `[INFO] Successfully cascaded ${pendingApprovers.total} ${role.name} approvers`,
      );
    } catch (error) {
      this.fastify.log.error(
        `[ERROR] Failed to cascade ${role.name} approver in canvass`,
      );
      this.fastify.log.error(error);
      throw error;
    }
  }

  async cascadeSupervisorId({ transaction, userId, supervisorId }) {
    const { CANVASS_APPROVER_STATUS } = this.constants.canvass;
    const requisitions = await this.requisitionRepository.findAll({
      where: { assignedTo: userId },
      include: [
        {
          association: 'canvassRequisitions',
          as: 'canvassRequisitions',
          required: true,
        },
      ],
    });

    if (!requisitions.total) {
      this.fastify.log.info('No RS Canvass associated with this user');
      return;
    }

    const canvassRequisitionIds = requisitions.data
      .map((req) => req.canvassRequisitions?.id)
      .filter(Boolean);

    const level1Approvers = await this.canvassApproverRepository.findAll({
      where: {
        level: 1,
        isAdhoc: false,
        status: CANVASS_APPROVER_STATUS.PENDING,
        canvassRequisitionId: {
          [this.db.Sequelize.Op.in]: canvassRequisitionIds,
        },
      },
      transaction,
    });

    if (!level1Approvers.total) {
      this.fastify.log.info(
        `No level 1 approvers found with pending status (CanvassIds: ${canvassRequisitionIds.join(',')})`,
      );
      return;
    }

    const updateApproverIdUpdates = level1Approvers.data.map((approver) =>
      this.canvassApproverRepository.update(
        { id: approver.id },
        { userId: supervisorId },
        { transaction },
      ),
    );

    try {
      await Promise.all(updateApproverIdUpdates);
    } catch (error) {
      this.fastify.log.error(`[ERROR] Update Approver Updates`);
      this.fastify.log.error(error);
      throw error;
    }
  }

  async processSuppliers({
    isDraft,
    suppliers,
    assignedTo,
    transaction,
    canvassItemId,
    existingSuppliers,
    isUpdateItems = false,
    userFromToken,
    canvassId,
  }) {
    const { MODELS } = this.constants.attachment;
    const { CANVASS_ITEM_STATUS } = this.constants.canvass;
    const { COMMENT_TYPES, USER_TYPES } = this.constants.note;

    if (isUpdateItems) {
      const newSupplierIds = suppliers
        .filter((supplier) => supplier.id)
        .map((supplier) => supplier.id);

      const suppliersToDelete = existingSuppliers
        .filter((supplier) => !newSupplierIds.includes(supplier.id))
        .map((supplier) => supplier.id);

      if (suppliersToDelete.length > 0) {
        await this.canvassItemSupplierRepository.destroy(
          {
            id: {
              [this.db.Sequelize.Op.in]: suppliersToDelete,
            },
          },
          { transaction },
        );
      }
    }

    for (const supplier of suppliers) {
      let supplierData = {};

      if (supplier.id) {
        supplierData = await this.canvassItemSupplierRepository.findOne({
          where: { id: supplier.id },
          transaction,
        });

        if (!supplierData) {
          throw this.clientErrors.NOT_FOUND({
            message: `Supplier with ID ${supplier.id} not found`,
          });
        }

        await this.canvassItemSupplierRepository.update(
          { id: supplier.id },
          {
            ...supplier,
            canvassItemId,
            isSelected: isDraft ? false : (supplier.isSelected ?? false),
          },
          { transaction, returning: true },
        );
      } else {
        supplierData = await this.canvassItemSupplierRepository.create(
          {
            ...supplier,
            canvassItemId,
            isSelected: isDraft ? false : (supplier.isSelected ?? false),
          },
          { transaction },
        );
      }

      if (supplier.notes) {
        const canvassDetails =
          await this.canvassApproverRepository.getAllCanvassApprovers(
            canvassId,
          );

        const currentUserId = userFromToken.id;
        const isOneOfApprovers = canvassDetails?.data?.find(
          (approver) =>
            approver.userId === currentUserId ||
            approver.altApproverId === currentUserId,
        );

        const noteUserType = !!isOneOfApprovers
          ? USER_TYPES.APPROVER
          : USER_TYPES.REQUESTOR;

        await this.noteService.createNote(
          {
            model: MODELS.CANVASS_ITEM_SUPPLIERS,
            modelId: supplierData.id,
            userName: userFromToken.fullNameUser,
            userType: noteUserType,
            commentType: COMMENT_TYPES.NOTE,
            note: supplier.notes,
          },
          { transaction },
        );
      }

      if (supplier.attachmentIds?.length > 0) {
        const attachments = await this.attachmentRepository.findAll({
          where: {
            id: { [this.db.Sequelize.Op.in]: supplier.attachmentIds },
          },
          transaction,
        });

        if (attachments.total !== supplier.attachmentIds.length) {
          const foundAttachmentIds = new Set(attachments.data.map((a) => a.id));
          const missingAttachmentIds = supplier.attachmentIds.filter(
            (id) => !foundAttachmentIds.has(id),
          );

          throw this.clientErrors.BAD_REQUEST({
            message: `Some attachments were not found with attachment ID(s): ${missingAttachmentIds.join(', ')}`,
          });
        }

        await this.attachmentRepository.update(
          {
            modelId: 0,
            model: MODELS.CANVASS_ITEM_SUPPLIERS,
          },
          { modelId: supplierData.id },
          {
            where: {
              id: { [this.db.Sequelize.Op.in]: supplier.attachmentIds },
            },
            transaction,
          },
        );
      }
    }

    if (isUpdateItems) {
      const supplierCount = await this.canvassItemSupplierRepository.count({
        where: { canvassItemId },
        transaction,
      });

      await this.canvassItemRepository.update(
        { id: canvassItemId },
        {
          status:
            !isDraft && supplierCount > 0
              ? CANVASS_ITEM_STATUS.FOR_APPROVAL
              : CANVASS_ITEM_STATUS.NEW,
        },
        { transaction },
      );
    }
  }

  #validateCanvassItemsQuantity({
    requisitionItems,
    existingCanvassItems,
    canvassType,
    canvassItemDetails,
    skipSubmissionItemCheck,
    isSubmitting = true,
  }) {
    const { CANVASS_TYPE, CANVASS_ITEM_STATUS } = this.constants.canvass;

    const canvassedItems = requisitionItems.data.map((item) => {
      // FOR SUBMISSIONS QUANTITY
      // Check qty of currently being submitted items
      const forSubmissionItems = existingCanvassItems.data.filter(
        (canvass) =>
          canvass.requisitionItemListId === item.id &&
          canvass.status === CANVASS_ITEM_STATUS.FOR_SUBMISSION,
      );

      const forSubmissionQty =
        forSubmissionItems?.reduce((acc, item) => {
          const itemQty = item.suppliers.reduce(
            (sum, supplier) => sum + parseFloat(supplier.quantity),
            0,
          );
          return acc + itemQty;
        }, 0) ?? 0;

      // FOR APPROVAL QUANTITY
      // Check qty of already submitted items
      const shouldExcludeCurrentItem =
        skipSubmissionItemCheck &&
        item.id === canvassItemDetails?.requisitionItemListId;

      const forApprovalItems = existingCanvassItems.data.filter(
        (canvass) =>
          canvass.requisitionItemListId === item.id &&
          canvass.status === CANVASS_ITEM_STATUS.FOR_APPROVAL &&
          (!shouldExcludeCurrentItem ||
            canvass.canvassRequisitionId !==
              canvassItemDetails?.canvassRequisitionId),
      );

      let forApprovalQty = 0;
      forApprovalQty =
        forApprovalItems?.reduce((acc, item) => {
          const itemQty = item.suppliers.reduce(
            (sum, supplier) => sum + parseFloat(supplier.quantity),
            0,
          );
          return acc + itemQty;
        }, 0) ?? 0;

      // CALCULATE NEW APPROVAL USING GIVEN SUPPLIER
      // WORKS FOR UPDATE ITEMS AS SUPPLIER ENDPOINT
      if (skipSubmissionItemCheck && !canvassItemDetails?.suppliers?.length) {
        throw this.clientErrors.BAD_REQUEST({
          message: `Canvassed item must have at least one supplier.`,
        });
      } else if (
        skipSubmissionItemCheck &&
        canvassItemDetails?.suppliers?.length &&
        item.id === canvassItemDetails?.requisitionItemListId
      ) {
        forApprovalQty += canvassItemDetails.suppliers.reduce(
          (acc, current) => acc + parseFloat(current.quantity),
          0,
        );
      }

      // CALCULATE QUANTITY FROM SELECTED SUPPLIERS
      const approvedItems = existingCanvassItems.data.filter(
        (canvass) =>
          canvass.requisitionItemListId === item.id &&
          canvass.status === CANVASS_ITEM_STATUS.APPROVED,
      );

      const approvedQty =
        approvedItems.reduce((acc, item) => {
          const selectedSuppliersQty = item.suppliers
            .filter((supplier) => supplier.isSelected)
            .reduce((sum, supplier) => sum + parseFloat(supplier.quantity), 0);
          return acc + selectedSuppliersQty;
        }, 0) ?? 0;

      // CALCULATE CLOSED QUANTITY
      const cancelledQty =
        approvedItems?.reduce(
          (acc, item) => parseFloat(acc + parseFloat(item.cancelledQty)),
          0,
        ) ?? 0;

      const parsedCancelledQty = parseFloat(cancelledQty.toFixed(3));
      const parsedApprovedQty = parseFloat(approvedQty.toFixed(3));
      const totalApprovedQty = parseFloat(
        parsedApprovedQty - parsedCancelledQty,
      );

      const parsedForApprovalQty = parseFloat(forApprovalQty.toFixed(3));
      const parsedForSubmissionQty = parseFloat(forSubmissionQty.toFixed(3));
      const parsedRequestedQty = parseFloat(item.quantity);

      // Calculate total canvassed quantity excluding cancelled items
      const canvassedQty = parseFloat(
        (
          parsedForApprovalQty +
          parsedForSubmissionQty +
          totalApprovedQty
        ).toFixed(3),
      );

      // Calculate remaining quantity considering cancelled items
      // Remaining = Requested - For Approval - Total Approved Qty (Approved - Cancelled)
      const remainingQty = parseFloat(
        (parsedRequestedQty - parsedForApprovalQty - totalApprovedQty).toFixed(
          3,
        ),
      );

      return {
        id: item.id,
        itemId: item.itemId,
        itemName:
          item?.itemDetails?.itmDes ||
          item?.itemDetails?.name ||
          item?.itemDetails?.itemName,
        requestedQty: parsedRequestedQty,
        itemType: item.itemType,
        forApprovalQty: parsedForApprovalQty,
        forSubmissionQty: parsedForSubmissionQty,
        approvedQty: parsedApprovedQty,
        totalApprovedQty: totalApprovedQty,
        cancelledQty: parsedCancelledQty,
        canvassedQty,
        remainingQty: remainingQty > 0 ? remainingQty : 0,
        includedInSubmission: !!forSubmissionItems.find(
          (canvass) => canvass.requisitionItemListId === item.id,
        ),
      };
    });

    if (!isSubmitting) return canvassedItems;

    if (
      CANVASS_TYPE.OFM === canvassType ||
      CANVASS_TYPE.OFM_TOM === canvassType
    ) {
      canvassedItems.forEach((item) => {
        if (
          item.requestedQty === item.forApprovalQty &&
          item.includedInSubmission
        ) {
          throw this.clientErrors.VALIDATION_ERROR({
            message: `Item "${item.itemName}" has already reached the maximum requested quantity of ${item.requestedQty}.`,
          });
        }

        if (
          item.requestedQty < item.canvassedQty &&
          CANVASS_TYPE.OFM_TOM === canvassType
        ) {
          throw this.clientErrors.VALIDATION_ERROR({
            message: `Item "${item.itemName}" exceeds the requested quantity by ${(item.requestedQty - item.canvassedQty).toFixed(3)}.`,
          });
        }
      });
    }

    if (
      CANVASS_TYPE.NON_OFM === canvassType ||
      CANVASS_TYPE.NON_OFM_TOM === canvassType
    ) {
      canvassedItems.forEach((item) => {
        if (
          item.requestedQty === item.forApprovalQty &&
          item.includedInSubmission
        ) {
          throw this.clientErrors.VALIDATION_ERROR({
            message: `Item "${item.itemName}" has already reached the maximum requested quantity of ${item.requestedQty}.`,
          });
        }
      });
    }

    return canvassedItems;
  }

  async validateSelectedSuppliers({
    transaction,
    requisitionId,
    selectedSuppliers,
  }) {
    const { CANVASS_ITEM_STATUS } = this.constants.canvass;

    const requisitionItems =
      await this.requisitionItemListRepository.getRequisitionAddItems(
        requisitionId,
        { transaction },
      );

    if (!requisitionItems.total) {
      throw this.clientErrors.NOT_FOUND({
        message: `Requisition with id of ${requisitionId} has no items.`,
      });
    }

    const existingCanvassItems = await this.canvassItemRepository.findAll({
      where: {
        requisitionId,
        status: {
          [this.db.Sequelize.Op.in]: [
            CANVASS_ITEM_STATUS.APPROVED,
            CANVASS_ITEM_STATUS.FOR_APPROVAL,
            CANVASS_ITEM_STATUS.FOR_SUBMISSION,
          ],
        },
      },
      include: [
        {
          association: 'suppliers',
          as: 'suppliers',
          required: false,
        },
      ],
      paginate: false,
      transaction,
    });

    const canvassedItemSelectedSuppliers = selectedSuppliers.map((supplier) => {
      return supplier.id;
    });

    const canvassedItems = requisitionItems.data.map((item) => {
      const forSubmissionItems = existingCanvassItems.data.filter(
        (canvass) =>
          canvass.requisitionItemListId === item.id &&
          canvassedItemSelectedSuppliers.some((id) =>
            canvass.suppliers.some((supplier) => supplier.id === id),
          ),
      );

      const forApprovalItems = existingCanvassItems.data.filter(
        (canvass) =>
          canvass.requisitionItemListId === item.id &&
          canvass.status === CANVASS_ITEM_STATUS.FOR_APPROVAL,
      );

      let forApprovalQty = 0;
      forApprovalQty =
        forApprovalItems?.reduce((acc, item) => {
          const itemQty = item.suppliers.reduce(
            (sum, supplier) => sum + parseFloat(supplier.quantity),
            0,
          );
          return acc + itemQty;
        }, 0) ?? 0; //

      // CALCULATE QUANTITY FROM SELECTED SUPPLIERS
      const approvedItems = existingCanvassItems.data.filter(
        (canvass) =>
          canvass.requisitionItemListId === item.id &&
          canvass.status === CANVASS_ITEM_STATUS.APPROVED,
      );

      const approvedQty =
        approvedItems.reduce((acc, item) => {
          const selectedSuppliersQty = item.suppliers
            .filter((supplier) => supplier.isSelected)
            .reduce((sum, supplier) => sum + parseFloat(supplier.quantity), 0);
          return acc + selectedSuppliersQty;
        }, 0) ?? 0;

      // CALCULATE CLOSED QUANTITY
      const cancelledQty =
        approvedItems?.reduce(
          (acc, item) => parseFloat(acc + parseFloat(item.cancelledQty)),
          0,
        ) ?? 0;

      const parsedCancelledQty = parseFloat(cancelledQty.toFixed(3));
      const parsedApprovedQty = parseFloat(approvedQty.toFixed(3));
      const totalApprovedQty = parseFloat(
        parsedApprovedQty - parsedCancelledQty,
      );

      const parsedForApprovalQty = parseFloat(forApprovalQty.toFixed(3));
      const parsedRequestedQty = parseFloat(item.quantity);

      // Calculate total canvassed quantity excluding cancelled items
      const canvassedQty = parseFloat(
        (parsedForApprovalQty + totalApprovedQty).toFixed(3),
      );

      // Calculate remaining quantity considering cancelled items
      // Remaining = Requested - For Approval - Total Approved Qty (Approved - Cancelled)
      const remainingQty = parseFloat(
        (parsedRequestedQty - parsedForApprovalQty - totalApprovedQty).toFixed(
          3,
        ),
      );
      const itemName =
        item?.itemDetails?.itmDes ||
        item?.itemDetails?.name ||
        item?.itemDetails?.itemName;

      return {
        id: item.id,
        itemId: item.itemId,
        itemName: itemName,
        requestedQty: parsedRequestedQty,
        itemType: item.itemType,
        forApprovalQty: parsedForApprovalQty,
        approvedQty: parsedApprovedQty,
        totalApprovedQty: totalApprovedQty,
        cancelledQty: parsedCancelledQty,
        canvassedQty,
        remainingQty: remainingQty,
        includedInSubmission: !!forSubmissionItems.find(
          (canvass) => canvass.requisitionItemListId === item.id,
        ),
      };
    });

    const overQuantity = canvassedItems.reduce((acc, item) => {
      if (item.remainingQty < 0 && item.includedInSubmission) {
        acc.push(item);
      }

      return acc;
    }, []);

    if (overQuantity.length === 1) {
      throw this.clientErrors.VALIDATION_ERROR({
        message: `Item "${overQuantity[0].itemName}" exceeds the requested quantity by ${Math.abs(
          overQuantity[0].remainingQty,
        )}.`,
      });
    } else if (overQuantity.length > 1) {
      throw this.clientErrors.VALIDATION_ERROR({
        message: `Items exceed the requested quantity by a total of ${overQuantity.reduce(
          (acc, item) => acc + Math.abs(item.remainingQty),
          0,
        )}.`,
      });
    }
  }

  async checkItemsValidity({
    transaction,
    canvassType,
    requisitionId,
    canvassItemDetails = null,
    skipSubmissionItemCheck = false,
    isSubmitting = true,
  }) {
    const { CANVASS_ITEM_STATUS } = this.constants.canvass;

    const requisitionItems =
      await this.requisitionItemListRepository.getRequisitionAddItems(
        requisitionId,
        { transaction },
      );

    if (!requisitionItems.total) {
      return;
    }

    const existingCanvassItems = await this.canvassItemRepository.findAll({
      where: {
        requisitionId,
        status: {
          [this.db.Sequelize.Op.in]: [
            CANVASS_ITEM_STATUS.APPROVED,
            CANVASS_ITEM_STATUS.FOR_APPROVAL,
            CANVASS_ITEM_STATUS.FOR_SUBMISSION,
          ],
        },
      },
      include: [
        {
          association: 'suppliers',
          as: 'suppliers',
          required: false,
        },
      ],
      paginate: false,
      transaction,
    });

    return this.#validateCanvassItemsQuantity({
      requisitionItems,
      existingCanvassItems,
      canvassType,
      canvassItemDetails,
      skipSubmissionItemCheck,
      isSubmitting,
    });
  }

  async #processCanvassItems({
    isCanvassNew,
    canvassId,
    requisitionId,
    addItems,
    updateItems,
    deleteItems,
    isDraft,
    transaction,
    assignedTo,
    userFromToken,
  }) {
    const { CANVASS_ITEM_STATUS } = this.constants.canvass;

    /* Delete Items */
    if (deleteItems.length > 0 && !isCanvassNew) {
      await this.canvassItemRepository.destroy(
        {
          canvassRequisitionId: canvassId,
          requisitionItemListId: {
            [this.db.Sequelize.Op.in]: deleteItems.map((item) => item.id),
          },
        },
        { transaction },
      );
    }

    /* Add Items */
    for (const item of addItems) {
      const hasSupplier = item.suppliers?.length > 0;
      const existingCanvassItem = await this.canvassItemRepository.findOne({
        where: {
          canvassRequisitionId: canvassId,
          requisitionItemListId: item.requisitionItemListId,
        },
        transaction,
      });

      if (existingCanvassItem) {
        throw this.clientErrors.BAD_REQUEST({
          message: `Item with requisitionItemListId ${item.requisitionItemListId} already exists in this canvass sheet`,
        });
      }

      const newCanvassItem = await this.canvassItemRepository.create(
        {
          canvassRequisitionId: canvassId,
          requisitionId,
          requisitionItemListId: item.requisitionItemListId,
          status: CANVASS_ITEM_STATUS.FOR_SUBMISSION,
        },
        { transaction },
      );

      if (hasSupplier) {
        await this.processSuppliers({
          suppliers: item.suppliers,
          canvassItemId: newCanvassItem.id,
          isDraft,
          assignedTo,
          transaction,
          userFromToken,
          canvassId,
        });
      }
    }

    if (isCanvassNew) {
      return;
    }

    /* Update Items */
    for (const item of updateItems) {
      const existingCanvassItem = await this.canvassItemRepository.findOne({
        where: { id: item.id },
        transaction,
      });

      if (!existingCanvassItem) {
        throw this.clientErrors.BAD_REQUEST({
          message: `Canvass item with id ${item.id} does not exist`,
        });
      }

      const hasSupplier = item.suppliers?.length > 0;
      const existingSuppliers =
        await this.canvassItemSupplierRepository.findAll({
          where: { canvassItemId: item.id },
          transaction,
        });

      if (hasSupplier) {
        await this.processSuppliers({
          isDraft,
          assignedTo,
          transaction,
          isUpdateItems: true,
          canvassItemId: item.id,
          suppliers: item.suppliers,
          existingSuppliers: existingSuppliers.data,
          userFromToken,
          canvassId,
        });
      }
    }

    const toUpdateItemsStatus = updateItems.map((item) => item.id);
    await this.canvassItemRepository.update(
      {
        id: toUpdateItemsStatus,
      },
      { status: CANVASS_ITEM_STATUS.FOR_SUBMISSION },
      { transaction },
    );
  }

  groupSuppliersByType(items = []) {
    const { SUPPLIER_TYPE } = this.constants.canvass;
    const groupedBySupplierType = {};

    items.forEach((item) => {
      item.suppliers?.forEach((supplier) => {
        const { supplierType = SUPPLIER_TYPE.SUPPLIER } = supplier;
        if (!groupedBySupplierType[supplierType]) {
          groupedBySupplierType[supplierType] = [];
        }

        groupedBySupplierType[supplierType].push(supplier);
      });
    });

    return groupedBySupplierType;
  }

  async generateCanvassDashboardData(canvassId) {
    const { CANVASS_STATUS } = this.constants.canvass;

    const [canvass, canvassItemList, canvassApprovers] = await Promise.all([
      this.getExistingCanvass(canvassId),
      this.canvassItemService.getAllCanvassItems(
        {
          canvassId,
          order: [
            ['requisitionItem', 'item', 'isSteelbars', 'ASC'],
            ['createdAt', 'DESC'],
          ],
        },
        {
          paginate: false,
        },
      ),
      this.canvassApproverRepository.getAllCanvassApprovers(canvassId),
    ]);

    const requisition = await this.requisitionService.getExistingRequisition(
      canvass.requisitionId,
    );

    const { companyCode } = canvass.requisition;
    const { csNumber, csLetter, draftCsNumber } = canvass;

    const isDraft = canvass.csNumber === null || canvass.csNumber === '';

    const canvassNumber = isDraft
      ? `CS-TMP-${companyCode}${csLetter}${draftCsNumber}`
      : `CS-${companyCode}${csLetter}${csNumber}`;

    const mappedCanvassApprover = canvassApprovers.data.map((approver) => {
      const { isAdhoc, level, approver: levelApprover } = approver;

      return {
        level,
        isAdhoc,
        approverName: levelApprover ? levelApprover.fullName : '--',
      };
    });

    const {
      chargeTo,
      purpose,
      company,
      project,
      department,
      chargeToCompany,
      chargeToSupplier,
      chargeToProject,
      createdByUser,
      dateRequired,
    } = requisition;

    let chargeToDetails = '';
    if (chargeTo === 'company') chargeToDetails = chargeToCompany?.name;
    else if (chargeTo === 'supplier') chargeToDetails = chargeToSupplier?.name;
    else if (chargeTo === 'project') chargeToDetails = chargeToProject?.name;

    const canvassItems = canvassItemList.data.map((item, index) => {
      const { requisitionItem, suppliers } = item;

      const updatedSuppliers = suppliers.map((supplier) => ({
        supplierName:
          supplier?.supplierName ??
          supplier?.supplierDetails?.name ??
          supplier?.name,
        unitPrice: supplier.unitPrice,
        discountedPrice: this.utils.calculateDiscountedPrice(
          supplier.unitPrice,
          supplier.discountType,
          supplier.discountValue,
          supplier.quantity,
        ),
      }));

      return {
        itemNum: index + 1,
        quantity: requisitionItem.quantity,
        unit: requisitionItem?.itemDetails?.unit || '---',
        itemName:
          requisitionItem?.itemDetails?.itmDes ||
          requisitionItem?.itemDetails?.name ||
          requisitionItem?.itemDetails?.itemName ||
          '---',
        suppliers: updatedSuppliers,
        note: requisitionItem.notes,
        isSteebars: requisitionItem?.item?.isSteelbars,
      };
    });

    const { pagesData, totalPages, totalItems } =
      this.utils.paginateItemsWithDifferentSizes(canvassItems, 7, 9);

    const data = {
      canvassHeader: project?.name || company?.name || '',
      canvassNumber,
      purpose,
      companyName: company?.name,
      projectName: project?.name,
      department: department?.name,
      datePrepared: new Date(),
      dateRequired,
      createdByUser:
        `${createdByUser.firstName} ${createdByUser?.lastName}`.trim(),
      chargeToDetails,
      canvassItems,
      pagesData,
      totalPages,
      totalItems,
      canvassApprovers: mappedCanvassApprover,
    };

    return data;
  }

  async isSupplierSuspended(suppliers) {
    for (const supplier of suppliers.data) {
      if (supplier.supplierType !== 'supplier') {
        continue;
      }

      const foundSuspended = await this.supplierRepository.findOne({
        where: { id: supplier.supplierId, status: 'SUSPENDED' },
      });

      if (foundSuspended) {
        return true;
      }
    }
    return false;
  }
}

module.exports = CanvassService;
