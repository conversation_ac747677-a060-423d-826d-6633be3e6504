class RoleService {
  constructor (container) {
    const { constants, roleRepository, db, userRepository, clientErrors } =
      container;

    this.db = db;
    this.userConstants = constants.user;
    this.roleRepository = roleRepository;
    this.userRepository = userRepository;
    this.clientErrors = clientErrors;
  }

  async getRolesByUserType(requesterRole) {
    let whereClause = {};
    const { USER_TYPES } = this.userConstants;

    if (requesterRole === USER_TYPES.ROOT_USER) {
      whereClause = {
        name: USER_TYPES.ADMIN,
      };
    } else {
      whereClause = {
        name: {
          [this.db.Sequelize.Op.notIn]: [
            USER_TYPES.ROOT_USER,
            USER_TYPES.ADMIN,
          ],
        },
      };
    }

    return await this.roleRepository.findAll({
      paginate: false,
      where: whereClause,
    });
  }

  async getPurchasingHeadUserId() {
    const { id } = await this.roleRepository.findOne({
      where: { name: 'Purchasing Head' },
    });

    return await this.userRepository.findOne({
      where: { roleId: id },
    });
  }

  async getSyncRecipientsRoleId() {
    const { USER_TYPES } = this.userConstants;

    const roles = await this.roleRepository.findAll({
      paginate: false,
      attributes: ['id'],
      where: {
        name: {
          [this.db.Sequelize.Op.in]: [
            USER_TYPES.ADMIN,
            USER_TYPES.PURCHASING_ADMIN,
          ],
        },
      },
    });

    return roles.data.map((role) => role.id);
  }
}

module.exports = RoleService;
