const { z } = require('zod');
const { createIdParamsSchema, createNumberSchema } = require('../../app/utils');
const { sort, note } = require('../constants');
const { sortSchema } = require('./sortEntity');
const { normalize } = require('./utils')

function noteSchema({ label, maxMessage, regexMessage }) {
  const fieldError = label ? stringFieldError(label) : undefined
  return z
    .string(fieldError)
    .transform(v => normalize(v))
    .pipe(
      z.string()
        .max(500, maxMessage)
        .regex(
          note.REGEX,
          regexMessage
        )
    )
    .optional()
}

const createDeliveryReceiptSchema = z
  .object({
    requisitionId: createIdParamsSchema('Requisition ID'),
    poId: createIdParamsSchema('Purchase Order ID'),
    supplier: z.string(),
    isDraft: z.string().refine((val) => val === 'true' || val === 'false', {
      message: 'isDraft must be a string that is either "true" or "false"',
    }),
    note: noteSchema({
      maxMessage: 'Delivery report note must not exceed 500 characters',
      regexMessage: 'Notes contain invalid characters.'
    }),
    invoiceNumber: z.string().optional(),
    supplierDeliveryIssuedDate: z.string().date().optional(),
    issuedDate: z.string().date(),
    items: z.array(
      z
        .object({
          poItemId: z.number({ required_error: 'PO Item ID is required' }),
          itemId: z.number({ required_error: 'Item ID is required' }),
          accountCode: z.string().optional(),
          poId: createIdParamsSchema('Purchase Order ID'),
          itemDes: z.string(),
          qtyOrdered: z
            .number()
            .min(0.01, 'Quantity ordered must be more than 0')
            .multipleOf(0.001, 'Quantity must have at most 3 decimal places'),
          qtyDelivered: z
            .number()
            .min(0, 'Quantity delivered must be at least 0')
            .multipleOf(0.001, 'Quantity must have at most 3 decimal places'),
          qtyReturned: z
            .number()
            .min(0, 'Quantity returned must be at least 0')
            .multipleOf(0.001, 'Quantity must have at most 3 decimal places'),
          dateDelivered: z.string().transform(date => {
            const d = new Date(date);
            return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`; // YYYY-MM-DD format
          }),
          unit: z.string(),
          notes: noteSchema({
            maxMessage: 'Delivery item note must not exceed 500 characters',
            regexMessage: 'Delivery item note contains invalid characters.'
          })
        })
        .strict()
        .refine((data) => data.qtyDelivered <= data.qtyOrdered, {
          message:
            'Quantity delivered must be less than or equal to the quantity ordered.',
        })
        .refine(
          (data) => data.qtyReturned <= data.qtyOrdered - data.qtyDelivered,
          {
            message: 'Quantity returned is invalid.',
          },
        ),
    ),
    attachmentIds: z.array(createNumberSchema('Attachment ID')).optional(),
  })
  .strict()
  .refine((data) => data.items.every((item) => item.poId == data.poId), {
    message: "Each item's poId must match the main poId.",
    path: ['items'],
  });

const getDeliveryReceiptByIdSchema = z.object({
  id: createIdParamsSchema('Delivery report ID'),
});

const updateDeliveryReceiptSchema = z
  .object({
    requisitionId: createIdParamsSchema('Requisition ID'),
    poId: createIdParamsSchema('Purchase Order ID'),
    supplier: z.string(),
    isDraft: z.string().refine((val) => val === 'true' || val === 'false', {
      message: 'isDraft must be a string that is either "true" or "false"',
    }),
    note: noteSchema({
      maxMessage: 'Delivery report note must not exceed 500 characters',
      regexMessage: 'Notes contain invalid characters.'
    }),
    invoiceNumber: z.string().optional(),
    supplierDeliveryIssuedDate: z.string().date().optional(),
    issuedDate: z.string().date(),
    items: z.array(
      z
        .object({
          id: z
            .number({
              required_error: 'ID for delivery item record is required',
            })
            .optional(),
          poItemId: z.number({ required_error: 'PO Item ID is required' }),
          itemId: z.number({ required_error: 'Item ID is required' }),
          accountCode: z.string().optional(),
          poId: createIdParamsSchema('Purchase Order ID'),
          itemDes: z.string(),
          qtyOrdered: z
            .number()
            .min(0.01, 'Quantity ordered must be more than 0')
            .multipleOf(0.001, 'Quantity must have at most 3 decimal places'),
          qtyDelivered: z
            .number()
            .min(0, 'Quantity delivered must be at least 0')
            .multipleOf(0.001, 'Quantity must have at most 3 decimal places'),
          qtyReturned: z
            .number()
            .min(0, 'Quantity returned must be at least 0')
            .multipleOf(0.001, 'Quantity must have at most 3 decimal places'),
          dateDelivered: z.string().transform(date => {
            const d = new Date(date);
            return `${d.getFullYear()}-${String(d.getMonth() + 1).padStart(2, '0')}-${String(d.getDate()).padStart(2, '0')}`; // YYYY-MM-DD format
          }),
          unit: z.string(),
          notes: z
            .string()
            .transform(v => normalize(v))
            .pipe(
              z
                .string()
                .max(500, 'Delivery item note must not exceed 500 characters')
                .refine(
                  (val) => {
                    return note.REGEX.test(val.replace(/©/g, ''));
                  },
                  {
                    message: 'Delivery item note contains invalid characters.',
                  },
                )
            )
            .optional(),
        })
        .strict()
        .refine((data) => data.qtyDelivered <= data.qtyOrdered, {
          message:
            'Quantity delivered must be less than or equal to the quantity ordered.',
        })
        .refine(
          (data) => data.qtyReturned <= data.qtyOrdered - data.qtyDelivered,
          {
            message: 'Quantity returned is invalid.',
          },
        ),
    ),
    currentAttachments: z
      .array(
        z
          .object({
            id: createIdParamsSchema('Attachment ID'),
            modelId: createIdParamsSchema('Model ID'),
            fileName: z.string(),
          })
          .strict(),
      )
      .optional(),
    attachmentIds: z.array(createNumberSchema('Attachment ID')).optional(),
  })
  .strict()
  .refine((data) => data.items.every((item) => item.poId == data.poId), {
    message: "Each item's poId must match the main poId.",
    path: ['items'],
  })
  .refine((data) => data.items.every((item) => item.poId == data.poId), {
    message: "Each item's poId must match the main poId.",
    path: ['items'],
  });

const getDeliveryReceiptsFromRequisitionParamsSchema = z.object({
  requisitionId: createIdParamsSchema('Requisition ID'),
});

const getDeliveryReceiptsFromRequisitionQuerySchema = z.object({
  search: z.string().optional(),
  sortBy: sortSchema(sort.DELIVERY_RECEIPT_SORT_COLUMNS).optional(),
  page: z.string().regex(/^\d+$/).transform(Number).optional(),
  limit: z.string().regex(/^\d+$/).transform(Number).optional(),
});

const getDeliveryReceiptItemsQuerySchema = z
  .object({
    sortBy: sortSchema(sort.DELIVERY_RECEIPT_ITEMS_COLUMNS).optional(),
    page: z.string().regex(/^\d+$/).transform(Number).optional(),
    paginate: z.union([z.boolean(), z.enum(['true', 'false'])]).optional(),
    limit: z.string().regex(/^\d+$/).transform(Number).optional(),
  })
  .strict();

const getDeliveryReceiptsFromPurchaseOrderQuerySchema = z
  .object({
    noInvoice: z
      .string()
      .refine((val) => val === 'true' || val === 'false', {
        message: 'noInvoice must be a string that is either "true" or "false"',
      })
      .optional(),
  })
  .strict();

module.exports = {
  createDeliveryReceiptSchema,
  getDeliveryReceiptByIdSchema,
  updateDeliveryReceiptSchema,
  getDeliveryReceiptsFromRequisitionParamsSchema,
  getDeliveryReceiptsFromRequisitionQuerySchema,
  getDeliveryReceiptItemsQuerySchema,
  getDeliveryReceiptsFromPurchaseOrderQuerySchema,
};
