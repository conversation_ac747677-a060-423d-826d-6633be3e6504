const BaseRepository = require('./baseRepository');
const { PO_STATUS } = require('../../domain/constants/purchaseOrderConstants');

class PurchaseOrderRepository extends BaseRepository {
  constructor({ db }) {
    super(db.purchaseOrderModel);
    this.db = db;
  }

  async getPOWithItsDeliveryReceipts(purchaseOrderId, invoiceId = null) {
    return this.getById(purchaseOrderId, {
      attributes: ['id', 'requisitionId', 'status'],
      include: [
        {
          model: this.db.deliveryReceiptModel,
          as: 'deliveryReceipts',
          where: {
            isDraft: false,
            invoiceId: {
              [this.db.Sequelize.Op.or]: [null, invoiceId],
            },
          },
          required: false,
        },
      ],
    });
  }

  async getPurchaseOrderTotals(purchaseOrderId) {
    const sqlQuery = `
      WITH price_data AS (
        SELECT
          cis.unit_price,
          poi.quantity_purchased,
          cis.discount_type,
          cis.discount_value,
          (cis.unit_price * poi.quantity_purchased) AS base_amount,
          CASE
            WHEN cis.discount_type = 'fixed' THEN cis.discount_value * poi.quantity_purchased
            WHEN cis.discount_type = 'percent' THEN (cis.unit_price * (cis.discount_value / 100)) * poi.quantity_purchased
            ELSE 0
          END AS discount
        FROM
          purchase_order_items poi
        JOIN
          canvass_item_suppliers cis ON poi.canvass_item_supplier_id = cis.id
        WHERE
          poi.purchase_order_id = :purchaseOrderId
      ),
      discount_adjustment AS (
        SELECT
          po.id,
          (CASE WHEN po.is_added_discount_percentage THEN 1 ELSE 0 END) AS is_percentage,
          po.added_discount,
          COALESCE(po.withholding_tax_deduction, 0) AS withholding_tax_deduction,
          COALESCE(po.delivery_fee, 0) AS delivery_fee,
          COALESCE(po.tip, 0) AS tip,
          COALESCE(po.extra_charges, 0) AS extra_charges
        FROM
          purchase_orders po
        WHERE
          po.id = :purchaseOrderId
      )
      SELECT
        SUM(p.base_amount) AS total_base_amount,

        SUM(ABS(p.discount)) + 
          CASE
            WHEN MAX(d.is_percentage) = 1 THEN 
              (SUM(p.base_amount) - SUM(p.discount)) * (MAX(d.added_discount) / 100.0)
            ELSE 
              MAX(d.added_discount)
          END AS total_discount_amount,


        (SUM(p.base_amount) - SUM(p.discount)) -
          CASE
            WHEN MAX(d.is_percentage) = 1 THEN 
              (SUM(p.base_amount) - SUM(p.discount)) * (MAX(d.added_discount) / 100.0)
            ELSE MAX(d.added_discount)
          END AS total_discounted_amount,

        (MAX(d.delivery_fee) + MAX(d.tip) + MAX(d.extra_charges) - MAX(d.withholding_tax_deduction)) AS total_additional_fees,

        ((SUM(p.base_amount) - SUM(p.discount)) -
          CASE
            WHEN MAX(d.is_percentage) = 1 THEN 
              (SUM(p.base_amount) - SUM(p.discount)) * (MAX(d.added_discount) / 100.0)
            ELSE MAX(d.added_discount)
          END -
          MAX(d.withholding_tax_deduction) +
          MAX(d.delivery_fee) + MAX(d.tip) + MAX(d.extra_charges)) AS grand_total
      FROM
        price_data p
      CROSS JOIN
        discount_adjustment d
    `;

    const [result] = await this.db.sequelize.query(sqlQuery, {
      replacements: { purchaseOrderId },
      type: this.db.Sequelize.QueryTypes.SELECT,
    });

    const safeNumber = (val) => Number(parseFloat(val ?? 0));

    return {
      totalBaseAmount: safeNumber(result.total_base_amount),
      totalDiscountAmount: safeNumber(result.total_discount_amount),
      totalDiscountedAmount: safeNumber(result.total_discounted_amount),
      totalAdditionalFees: safeNumber(result.total_additional_fees),
      grandTotal: safeNumber(result.grand_total),
    };
  }
}

module.exports = PurchaseOrderRepository;
